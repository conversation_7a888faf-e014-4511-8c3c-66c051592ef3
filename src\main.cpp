#include <Arduino.h>
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <DHT.h>
#include <Wire.h>
#include <BH1750.h>
#include <Adafruit_INA219.h> // Add INA219 library

// Sensor Configuration
#define DHTPIN 12 // GPIO4 for DHT11
#define DHTTYPE DHT11
#define SDA_PIN 21 // GPIO21 for I2C SDA
#define SCL_PIN 22 // GPIO22 for I2C SCL

DHT dht(DHTPIN, DHTTYPE);
BH1750 lightMeter;
Adafruit_INA219 ina219; // INA219 object

// HiveMQ Cloud Root CA Certificate
const char* hivemq_root_ca = \
"-----BEGIN CERTIFICATE-----\n" \
"MIIFazCCA1OgAwIBAgIRAIIQz7DSQONZRGPgu2OCiwAwDQYJKoZIhvcNAQELBQAw\n" \
"TzELMAkGA1UEBhMCVVMxKTAnBgNVBAoTIEludGVybmV0IFNlY3VyaXR5IFJlc2Vh\n" \
"cmNoIEdyb3VwMRUwEwYDVQQDEwxJU1JHIFJvb3QgWDEwHhcNMTUwNjA0MTEwNDM4\n" \
"WhcNMzUwNjA0MTEwNDM4WjBPMQswCQYDVQQGEwJVUzEpMCcGA1UEChMgSW50ZXJu\n" \
"ZXQgU2VjdXJpdHkgUmVzZWFyY2ggR3JvdXAxFTATBgNVBAMTDElTUkcgUm9vdCBY\n" \
"MTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAK3oJHP0FDfzm54rVygc\n" \
"h77ct984kIxuPOZXoHj3dcKi/vVqbvYATyjb3miGbESTtrFj/RQSa78f0uoxmyF+\n" \
"0TM8ukj13Xnfs7j/EvEhmkvBioZxaUpmZmyPfjxwv60pIgbz5MDmgK7iS4+3mX6U\n" \
"A5/TR5d8mUgjU+g4rk8Kb4Mu0UlXjIB0ttov0DiNewNwIRt18jA8+o+u3dpjq+sW\n" \
"T8KOEUt+zwvo/7V3LvSye0rgTBIlDHCNAymg4VMk7BPZ7hm/ELNKjD+Jo2FR3qyH\n" \
"B5T0Y3HsLuJvW5iB4YlcNHlsdu87kGJ55tukmi8mxdAQ4Q7e2RCOFvu396j3x+UC\n" \
"B5iPNgiV5+I3lg02dZ77DnKxHZu8A/lJBdiB3QW0KtZB6awBdpUKD9jf1b0SHzUv\n" \
"KBds0pjBqAlkd25HN7rOrFleaJ1/ctaJxQZBKT5ZPt0m9STJEadao0xAH0ahmbWn\n" \
"OlFuhjuefXKnEgV4We0+UXgVCwOPjdAvBbI+e0ocS3MFEvzG6uBQE3xDk3SzynTn\n" \
"jh8BCNAw1FtxNrQHusEwMFxIt4I7mKZ9YIqioymCzLq9gwQbooMDQaHWBfEbwrbw\n" \
"qHyGO0aoSCqI3Haadr8faqU9GY/rOPNk3sgrDQoo//fb4hVC1CLQJ13hef4Y53CI\n" \
"rU7m2Ys6xt0nUW7/vGT1M0NPAgMBAAGjQjBAMA4GA1UdDwEB/wQEAwIBBjAPBgNV\n" \
"HRMBAf8EBTADAQH/MB0GA1UdDgQWBBR5tFnme7bl5AFzgAiIyBpY9umbbjANBgkq\n" \
"hkiG9w0BAQsFAAOCAgEAVR9YqbyyqFDQDLHYGmkgJykIrGF1XIpu+ILlaS/V9lZL\n" \
"ubhzEFnTIZd+50xx+7LSYK05qAvqFyFWhfFQDlnrzuBZ6brJFe+GnY+EgPbk6ZGQ\n" \
"3BebYhtF8GaV0nxvwuo77x/Py9auJ/GpsMiu/X1+mvoiBOv/2X/qkSsisRcOj/KK\n" \
"NFtY2PwByVS5uCbMiogziUwthDyC3+6WVwW6LLv3xLfHTjuCvjHIInNzktHCgKQ5\n" \
"ORAzI4JMPJ+GslWYHb4phowim57iaztXOoJwTdwJx4nLCgdNbOhdjsnvzqvHu7Ur\n" \
"TkXWStAmzOVyyghqpZXjFaH3pO3JLF+l+/+sKAIuvtd7u+Nxe5AW0wdeRlN8NwdC\n" \
"jNPElpzVmbUq4JUagEiuTDkHzsxHpFKVK7q4+63SM1N95R1NbdWhscdCb+ZAJzVc\n" \
"oyi3B43njTOQ5yOf+1CceWxG1bQVs5ZufpsMljq4Ui0/1lvh+wjChP4kqKOJ2qxq\n" \
"4RgqsahDYVvTH9w7jXbyLeiNdd8XM2w9U/t7y0Ff/9yi0GE44Za4rF2LN9d11TPA\n" \
"mRGunUHBcnWEvgJBQl9nJEiU0Zsnvgc/ubhPgXRR4Xq37Z0j4r7g1SgEEzwxA57d\n" \
"emyPxgcYxn/eR44/KJ4EBs+lVDR3veyJm+kXQ99b21/+jh5Xos1AnX5iItreGCc=\n" \
"-----END CERTIFICATE-----\n";

// MQTT Clients
WiFiClient espClientPlain;
WiFiClientSecure espClientSecure;
PubSubClient mqttClient(espClientPlain); // Will be reconfigured in setup()

// WiFi Configuration
const char *ssid = "Bismillah";
const char *password = "besoknaikhaji";

// MQTT Configuration
// ==================
// Set use_tls = true  for HiveMQ Cloud (secure, private, requires auth)
// Set use_tls = false for HiveMQ Public (plain, public, no auth needed)
const bool use_tls = true; // Change this to switch between TLS and Plain mode

// HiveMQ Cloud (TLS)
const char *mqtt_server_tls = "79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud";
const int mqtt_port_tls = 8883;
const char *mqtt_user_tls = "smart_lamp";
const char *mqtt_password_tls = "Lampupintar123";

// HiveMQ Public (Non-TLS) - Alternative
const char *mqtt_server_plain = "broker.hivemq.com";
const int mqtt_port_plain = 1883;
const char *mqtt_user_plain = "";
const char *mqtt_password_plain = "";

// Active configuration (will be set based on use_tls)
const char *mqtt_server;
int mqtt_port;
const char *mqtt_user;
const char *mqtt_password;
const char *mqtt_client_id = "ESP32_SmartLamp";

// MQTT Topics
const char *topic_sensors = "smartlamp/sensors/data";
const char *topic_status = "smartlamp/status";
const char *topic_heartbeat = "smartlamp/heartbeat";
const char *topic_command = "smartlamp/command";
const char *topic_response = "smartlamp/response";

// Timing Configuration
unsigned long interval = 600000; // Default: 10 minutes (600,000 ms)
unsigned long previousMillis = 0;
const unsigned long MIN_INTERVAL = 5000;    // Minimum 5 seconds
const unsigned long MAX_INTERVAL = 3600000; // Maximum 1 hour

// Status tracking
bool dhtStatus = false;
bool bh1750Status = false;
bool ina219Status = false;
bool wifiStatus = false;
bool mqttStatus = false;

// Function declarations
void configureMQTT();
void sendToMQTT(float temp, float hum, float lux, float voltage, float current);
void mqttCallback(char* topic, byte* payload, unsigned int length);
void processCommand(String command);
void setInterval(unsigned long newInterval);
void sendResponse(String message, bool success = true);
bool connectToMQTT();
bool verifyMQTTConnection();
void reconnectMQTT();
bool verifyWiFiConnection();
bool verifyDHT11();
bool verifyBH1750();
bool verifyINA219();
void printSystemStatus();
void reconnectWiFi();
void checkPowerSupply();

void setup()
{
  Serial.begin(115200);
  Serial.setDebugOutput(true);

  // Configure MQTT settings based on TLS preference
  configureMQTT();

  Serial.println("\n=== ESP32 Smart Lamp MQTT System Starting ===");
  Serial.printf("Chip Model: %s\n", ESP.getChipModel());
  Serial.printf("Chip Revision: %d\n", ESP.getChipRevision());
  Serial.printf("CPU Frequency: %d MHz\n", ESP.getCpuFreqMHz());  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("MQTT Mode: %s\n", use_tls ? "TLS/SSL" : "Plain");
  Serial.printf("MQTT Broker: %s:%d\n", mqtt_server, mqtt_port);
  if (strlen(mqtt_user) > 0) {
    Serial.printf("MQTT User: %s\n", mqtt_user);
  }
  Serial.println("===============================================");

  // Check power supply
  checkPowerSupply();

  // Initialize I2C with custom pins
  Wire.begin(SDA_PIN, SCL_PIN);
  Serial.printf("I2C initialized - SDA: GPIO%d, SCL: GPIO%d\n", SDA_PIN, SCL_PIN);
  
  // Scan for I2C devices
  Serial.println("Scanning I2C bus...");
  int deviceCount = 0;
  for (byte address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    if (Wire.endTransmission() == 0) {
      Serial.printf("I2C device found at address 0x%02X\n", address);
      deviceCount++;
    }
  }
  if (deviceCount == 0) {
    Serial.println("No I2C devices found!");
  } else {
    Serial.printf("Found %d I2C device(s)\n", deviceCount);
  }

  // Initialize and verify sensors
  Serial.println("\nInitializing sensors...");
  
  dht.begin();
  dhtStatus = verifyDHT11();
  
  bh1750Status = verifyBH1750();
  
  ina219Status = verifyINA219();

  // Initialize WiFi
  wifiStatus = verifyWiFiConnection();

  // Initialize MQTT
  if (wifiStatus) {
    mqttClient.setServer(mqtt_server, mqtt_port);
    mqttClient.setCallback(mqttCallback);
    mqttStatus = connectToMQTT();
  }

  // Print system status
  printSystemStatus();

  if (!wifiStatus) {
    Serial.println("WARNING: WiFi connection failed! System will continue with sensor readings only.");
  }

  if (!mqttStatus) {
    Serial.println("WARNING: MQTT connection failed! Data will not be transmitted.");
  }

  if (!dhtStatus || !bh1750Status || !ina219Status) {
    Serial.println("WARNING: Some sensors failed initialization!");
  }

  Serial.println("\nSetup completed. Starting main loop...");
}

void loop()
{
  // Handle MQTT client loop
  if (mqttStatus) {
    mqttClient.loop();
  }

  unsigned long currentMillis = millis();

  if (currentMillis - previousMillis >= interval)
  {
    previousMillis = currentMillis;

    Serial.println("\n=== Sensor Reading Cycle ===");

    // Verify WiFi connection before attempting to send data
    if (!verifyWiFiConnection()) {
      Serial.println("WiFi verification failed. Attempting reconnection...");
      reconnectWiFi();
      wifiStatus = verifyWiFiConnection();
    }

    // Verify MQTT connection
    if (wifiStatus && !verifyMQTTConnection()) {
      Serial.println("MQTT verification failed. Attempting reconnection...");
      reconnectMQTT();
      mqttStatus = verifyMQTTConnection();
    }

    // Verify sensors before reading
    if (!verifyDHT11()) {
      Serial.println("DHT11 verification failed!");
      dhtStatus = false;
    }
    
    if (!verifyBH1750()) {
      Serial.println("BH1750 verification failed!");
      bh1750Status = false;
    }
    
    if (!verifyINA219()) {
      Serial.println("INA219 verification failed!");
      ina219Status = false;
    }

    // Read sensors with enhanced validation
    float temp = NAN, hum = NAN, lux = -1, voltage = 0, current = 0;
    bool readingsValid = true;

    if (dhtStatus) {
      temp = dht.readTemperature();
      hum = dht.readHumidity();
      
      if (isnan(temp) || isnan(hum) || temp < -40 || temp > 80 || hum < 0 || hum > 100) {
        Serial.println("DHT11 reading validation failed!");
        readingsValid = false;
      }
    } else {
      readingsValid = false;
    }

    if (bh1750Status) {
      lux = lightMeter.readLightLevel();
      if (lux < 0 || lux > 100000) {
        Serial.println("BH1750 reading validation failed!");
        readingsValid = false;
      }
    } else {
      readingsValid = false;
    }

    if (ina219Status) {
      voltage = ina219.getBusVoltage_V();
      current = ina219.getCurrent_mA();
      
      if (voltage < 0 || voltage > 32 || current < -3200 || current > 3200) {
        Serial.println("INA219 reading validation failed!");
        readingsValid = false;
      }
    } else {
      readingsValid = false;
    }

    // Display readings
    Serial.println("\nSensor Readings:");
    if (dhtStatus) {
      Serial.printf("Temperature: %.1f°C\n", temp);
      Serial.printf("Humidity: %.1f%%\n", hum);
    } else {
      Serial.println("Temperature: SENSOR ERROR");
      Serial.println("Humidity: SENSOR ERROR");
    }
    
    if (bh1750Status) {
      Serial.printf("Light Level: %.1f lux\n", lux);
    } else {
      Serial.println("Light Level: SENSOR ERROR");
    }
    
    if (ina219Status) {
      Serial.printf("Bus Voltage: %.3f V\n", voltage);
      Serial.printf("Current: %.1f mA\n", current);
    } else {
      Serial.println("Bus Voltage: SENSOR ERROR");
      Serial.println("Current: SENSOR ERROR");
    }

    // Send data only if WiFi and MQTT are connected and readings are valid
    if (wifiStatus && mqttStatus && readingsValid) {
      sendToMQTT(temp, hum, lux, voltage, current);
    } else {
      if (!wifiStatus) {
        Serial.println("Skipping data transmission - WiFi not connected");
      }
      if (!mqttStatus) {
        Serial.println("Skipping data transmission - MQTT not connected");
      }
      if (!readingsValid) {
        Serial.println("Skipping data transmission - Invalid sensor readings");
      }
    }
    
    printSystemStatus();
    Serial.println("=== End Cycle ===\n");
  }
  
  // Small delay to prevent watchdog issues
  delay(100);
}

void sendToMQTT(float temp, float hum, float lux, float voltage, float current)
{
  Serial.println("\n=== Sending Data to MQTT ===");

  if (!verifyMQTTConnection()) {
    Serial.println("MQTT not available, cannot send data!");
    return;
  }

  // Create JSON payload
  StaticJsonDocument<200> doc;
  doc["timestamp"] = millis();
  doc["temperature"] = round(temp * 10) / 10.0; // 1 decimal place
  doc["humidity"] = round(hum * 10) / 10.0;     // 1 decimal place
  doc["light"] = round(lux * 10) / 10.0;        // 1 decimal place
  doc["voltage"] = round(voltage * 1000) / 1000.0; // 3 decimal places
  doc["current"] = round(current * 10) / 10.0;  // 1 decimal place
  doc["device_id"] = mqtt_client_id;

  String jsonString;
  serializeJson(doc, jsonString);

  Serial.println("Sending sensor data:");
  Serial.printf("Temperature: %.1f°C\n", temp);
  Serial.printf("Humidity: %.1f%%\n", hum);
  Serial.printf("Light Level: %.1f lux\n", lux);
  Serial.printf("Voltage: %.3fV\n", voltage);
  Serial.printf("Current: %.1fmA\n", current);
  Serial.println("JSON Payload: " + jsonString);

  unsigned long startTime = millis();
  bool result = mqttClient.publish(topic_sensors, jsonString.c_str());
  unsigned long responseTime = millis() - startTime;

  if (result) {
    Serial.println("✓ Data sent successfully to MQTT!");
    Serial.printf("Topic: %s\n", topic_sensors);
    Serial.printf("Response Time: %lu ms\n", responseTime);

    // Send heartbeat
    String heartbeat = "{\"device_id\":\"" + String(mqtt_client_id) + "\",\"uptime\":" + String(millis() / 1000) + ",\"free_heap\":" + String(ESP.getFreeHeap()) + "}";
    mqttClient.publish(topic_heartbeat, heartbeat.c_str());
  } else {
    Serial.println("✗ Failed to send data to MQTT!");
    Serial.println("Attempting to reconnect...");
    reconnectMQTT();
  }

  Serial.printf("Free heap after MQTT: %d bytes\n", ESP.getFreeHeap());
  Serial.println("=== End Data Transmission ===");
}

// WiFi verification function
bool verifyWiFiConnection() {
  Serial.println("\nVerifying WiFi connection...");
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.printf("WiFi already connected to: %s\n", WiFi.SSID().c_str());
    Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
    return true;
  }
  
  Serial.printf("Connecting to WiFi network: %s\n", ssid);
  WiFi.begin(ssid, password);
  
  unsigned long startTime = millis();
  int dots = 0;
  
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 20000) {
    delay(500);
    Serial.print(".");
    dots++;
    if (dots % 10 == 0) Serial.println();
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi connected successfully!");
    Serial.printf("SSID: %s\n", WiFi.SSID().c_str());
    Serial.printf("IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("Gateway: %s\n", WiFi.gatewayIP().toString().c_str());
    Serial.printf("DNS: %s\n", WiFi.dnsIP().toString().c_str());
    Serial.printf("Signal Strength: %d dBm\n", WiFi.RSSI());
    Serial.printf("MAC Address: %s\n", WiFi.macAddress().c_str());
    return true;
  } else {
    Serial.println("\nWiFi connection failed!");
    Serial.printf("WiFi Status: %d\n", WiFi.status());
    return false;
  }
}

// DHT11 verification function
bool verifyDHT11() {
  Serial.println("Verifying DHT11 sensor...");
  
  // Configure DHT pin as input with pullup
  pinMode(DHTPIN, INPUT_PULLUP);
  delay(100);
  
  // Try to read temperature and humidity multiple times
  for (int i = 0; i < 5; i++) {
    float temp = dht.readTemperature();
    float hum = dht.readHumidity();
    
    Serial.printf("DHT11 attempt %d - Raw readings: Temp=%.2f, Hum=%.2f\n", i + 1, temp, hum);
    
    if (!isnan(temp) && !isnan(hum)) {
      // DHT11 has specific ranges: temp 0-50°C, humidity 20-90%
      if (temp >= -10 && temp <= 60 && hum >= 0 && hum <= 100) {
        Serial.printf("DHT11 verified - Temp: %.1f°C, Humidity: %.1f%%\n", temp, hum);
        return true;
      } else {
        Serial.printf("DHT11 readings out of range - Temp: %.1f°C, Humidity: %.1f%%\n", temp, hum);
      }
    } else {
      Serial.println("DHT11 returned NaN values");
    }
    
    Serial.printf("DHT11 attempt %d failed, retrying...\n", i + 1);
    delay(3000); // DHT11 needs more time between readings
  }
  
  Serial.println("DHT11 verification failed after 5 attempts!");
  Serial.println("Check DHT11 connections:");
  Serial.printf("- Data pin should be connected to GPIO%d\n", DHTPIN);
  Serial.println("- VCC should be connected to 3.3V");
  Serial.println("- GND should be connected to GND");
  Serial.println("- Consider adding a 4.7kΩ pull-up resistor between DATA and VCC");
  
  return false;
}

// BH1750 verification function
bool verifyBH1750() {
  Serial.println("Verifying BH1750 light sensor...");
  
  // Check if BH1750 is present on I2C bus (address 0x23 or 0x5C)
  Wire.beginTransmission(0x23);
  if (Wire.endTransmission() != 0) {
    Wire.beginTransmission(0x5C);
    if (Wire.endTransmission() != 0) {
      Serial.println("BH1750 not found on I2C bus!");
      return false;
    } else {
      Serial.println("BH1750 found at address 0x5C");
    }
  } else {
    Serial.println("BH1750 found at address 0x23");
  }
  
  if (!lightMeter.begin(BH1750::CONTINUOUS_HIGH_RES_MODE)) {
    Serial.println("BH1750 initialization failed!");
    
    // Try different modes
    Serial.println("Trying different BH1750 modes...");
    if (lightMeter.begin(BH1750::ONE_TIME_HIGH_RES_MODE)) {
      Serial.println("BH1750 initialized in ONE_TIME_HIGH_RES_MODE");
    } else if (lightMeter.begin(BH1750::CONTINUOUS_LOW_RES_MODE)) {
      Serial.println("BH1750 initialized in CONTINUOUS_LOW_RES_MODE");
    } else {
      Serial.println("All BH1750 initialization modes failed!");
      return false;
    }
  }
  
  // Give sensor time to stabilize
  delay(500);
  
  // Try to read light level multiple times
  for (int i = 0; i < 5; i++) {
    float lux = lightMeter.readLightLevel();
    
    if (lux >= 0 && lux <= 100000) {
      Serial.printf("BH1750 verified - Light Level: %.1f lux\n", lux);
      return true;
    }
    
    Serial.printf("BH1750 attempt %d failed (reading: %.1f), retrying...\n", i + 1, lux);
    delay(1000);
  }
  
  Serial.println("BH1750 verification failed after 5 attempts!");
  return false;
}

// INA219 verification function
bool verifyINA219() {
  Serial.println("Verifying INA219 current sensor...");
  
  // Check if INA219 is present on I2C bus (address 0x40, 0x41, 0x44, or 0x45)
  byte ina219_addresses[] = {0x40, 0x41, 0x44, 0x45};
  bool found = false;
  byte foundAddress = 0x40; // Default address
  
  for (int i = 0; i < 4; i++) {
    Wire.beginTransmission(ina219_addresses[i]);
    if (Wire.endTransmission() == 0) {
      Serial.printf("INA219 found at address 0x%02X\n", ina219_addresses[i]);
      found = true;
      foundAddress = ina219_addresses[i];
      break;
    }
  }
  
  if (!found) {
    Serial.println("INA219 not found on I2C bus!");
    return false;
  }
  
  // Initialize INA219 (it will use the default address 0x40)
  if (!ina219.begin(&Wire)) {
    Serial.println("INA219 initialization failed!");
    return false;
  }
  
  // Configure INA219 for better accuracy
  ina219.setCalibration_32V_2A(); // Set calibration for 32V, 2A range
  
  // Give sensor time to stabilize
  delay(500);
  
  // Try to read voltage and current multiple times
  for (int i = 0; i < 5; i++) {
    float voltage = ina219.getBusVoltage_V();
    float current = ina219.getCurrent_mA();
    float shuntVoltage = ina219.getShuntVoltage_mV();
    
    // Check if readings are within reasonable range
    if (voltage >= 0 && voltage <= 32 && current >= -3200 && current <= 3200) {
      Serial.printf("INA219 verified - Bus Voltage: %.3fV, Current: %.1fmA, Shunt: %.3fmV\n", 
                   voltage, current, shuntVoltage);
      Serial.printf("INA219 operating at address 0x%02X\n", foundAddress);
      return true;
    }
    
    Serial.printf("INA219 attempt %d failed (V: %.3f, I: %.1f), retrying...\n", i + 1, voltage, current);
    delay(1000);
  }
  
  Serial.println("INA219 verification failed after 5 attempts!");
  return false;
}

// System status display function
void printSystemStatus() {
  Serial.println("\n=== System Status ===");
  Serial.printf("WiFi: %s\n", wifiStatus ? "✓ Connected" : "✗ Disconnected");
  if (wifiStatus) {
    Serial.printf("  IP: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("  RSSI: %d dBm\n", WiFi.RSSI());
  }
  Serial.printf("MQTT: %s\n", mqttStatus ? "✓ Connected" : "✗ Disconnected");
  if (mqttStatus) {
    Serial.printf("  Mode: %s\n", use_tls ? "TLS/SSL" : "Plain");
    Serial.printf("  Broker: %s:%d\n", mqtt_server, mqtt_port);
    Serial.printf("  Client: %s\n", mqtt_client_id);
    if (strlen(mqtt_user) > 0) {
      Serial.printf("  User: %s\n", mqtt_user);
    }
  }
  Serial.printf("DHT11: %s\n", dhtStatus ? "✓ Working" : "✗ Failed");
  Serial.printf("BH1750: %s\n", bh1750Status ? "✓ Working" : "✗ Failed");
  Serial.printf("INA219: %s\n", ina219Status ? "✓ Working" : "✗ Failed");
  Serial.printf("Interval: %lu ms (%.1fs / %.2fm)\n", interval, interval/1000.0, interval/60000.0);
  Serial.printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Uptime: %lu seconds\n", millis() / 1000);
  Serial.println("====================");
}

// WiFi reconnection function
void reconnectWiFi() {
  Serial.println("Attempting WiFi reconnection...");
  WiFi.disconnect();
  delay(1000);
  WiFi.begin(ssid, password);
  
  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && millis() - startTime < 15000) {
    delay(500);
    Serial.print(".");
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi reconnected successfully!");
    wifiStatus = true;
  } else {
    Serial.println("\nWiFi reconnection failed!");
    wifiStatus = false;
  }
}

// Power supply check function
void checkPowerSupply() {
  Serial.println("\n=== Power Supply Check ===");
  
  // Check ESP32 internal voltage (approximation)
  uint32_t voltage_mv = ESP.getFlashChipSize(); // This is not actual voltage, using alternative method
  
  // Better method: read from ADC if available
  // For now, we'll just check if the system is stable
  Serial.println("Checking 3.3V rail stability...");
  
  // Test I2C communication to verify power stability
  Wire.beginTransmission(0x00); // General call address
  byte error = Wire.endTransmission();
  
  if (error == 0) {
    Serial.println("✓ I2C bus appears stable");
  } else {
    Serial.println("⚠ I2C bus may have power issues");
  }
  
  // Check if we can read from built-in sensors
  Serial.println("Verifying power to sensors:");
  Serial.println("- DHT11 requires stable 3.3V on VCC pin");
  Serial.println("- BH1750 requires stable 3.3V on VCC pin"); 
  Serial.println("- INA219 requires stable 3.3V on VIN pin");
  Serial.println("- All sensors require common GND connection");
  
  Serial.printf("Free heap: %d bytes (should be >200KB for stable operation)\n", ESP.getFreeHeap());
  
  if (ESP.getFreeHeap() < 200000) {
    Serial.println("⚠ Warning: Low memory detected!");
  }
  
  Serial.println("=========================");
}

// Configure MQTT settings based on TLS preference
void configureMQTT() {
  if (use_tls) {
    // Use TLS configuration (HiveMQ Cloud)
    mqtt_server = mqtt_server_tls;
    mqtt_port = mqtt_port_tls;
    mqtt_user = mqtt_user_tls;
    mqtt_password = mqtt_password_tls;

    // Configure secure client
    espClientSecure.setCACert(hivemq_root_ca);
    espClientSecure.setInsecure(); // For testing, remove in production
    mqttClient.setClient(espClientSecure);

    Serial.println("MQTT configured for TLS/SSL connection");
  } else {
    // Use plain configuration (HiveMQ Public)
    mqtt_server = mqtt_server_plain;
    mqtt_port = mqtt_port_plain;
    mqtt_user = mqtt_user_plain;
    mqtt_password = mqtt_password_plain;

    // Configure plain client
    mqttClient.setClient(espClientPlain);

    Serial.println("MQTT configured for plain connection");
  }

  Serial.printf("Selected broker: %s:%d\n", mqtt_server, mqtt_port);
  if (strlen(mqtt_user) > 0) {
    Serial.printf("Authentication: %s\n", mqtt_user);
  } else {
    Serial.println("Authentication: None (Public broker)");
  }
}

// MQTT callback function
void mqttCallback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message arrived [");
  Serial.print(topic);
  Serial.print("] ");

  String message;
  for (int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  Serial.println(message);

  // Handle command messages
  if (String(topic) == topic_command) {
    Serial.println("Processing command: " + message);
    processCommand(message);
  }
}

// Connect to MQTT broker
bool connectToMQTT() {
  Serial.println("\nConnecting to MQTT broker...");
  Serial.printf("Server: %s:%d (%s)\n", mqtt_server, mqtt_port, use_tls ? "TLS/SSL" : "Plain");
  Serial.printf("Client ID: %s\n", mqtt_client_id);
  if (strlen(mqtt_user) > 0) {
    Serial.printf("Username: %s\n", mqtt_user);
  }

  // Generate unique client ID
  String clientId = String(mqtt_client_id) + "_" + String(random(0xffff), HEX);

  // Connect with or without credentials
  bool connected = false;
  if (strlen(mqtt_user) > 0) {
    connected = mqttClient.connect(clientId.c_str(), mqtt_user, mqtt_password);
  } else {
    connected = mqttClient.connect(clientId.c_str());
  }

  if (connected) {
    Serial.println("✓ MQTT connected successfully!");

    // Subscribe to command topic
    mqttClient.subscribe(topic_command);
    Serial.printf("Subscribed to command topic: %s\n", topic_command);

    // Send initial status
    String statusMsg = "{\"device_id\":\"" + String(mqtt_client_id) + "\",\"status\":\"online\",\"ip\":\"" + WiFi.localIP().toString() + "\",\"interval\":" + String(interval) + "}";
    mqttClient.publish(topic_status, statusMsg.c_str());

    return true;
  } else {
    Serial.printf("✗ MQTT connection failed, rc=%d\n", mqttClient.state());
    Serial.println("Trying again in 5 seconds...");
    return false;
  }
}

// Verify MQTT connection
bool verifyMQTTConnection() {
  if (mqttClient.connected()) {
    return true;
  } else {
    Serial.println("MQTT connection lost!");
    return false;
  }
}

// Reconnect to MQTT
void reconnectMQTT() {
  Serial.println("Attempting MQTT reconnection...");

  int attempts = 0;
  while (!mqttClient.connected() && attempts < 3) {
    if (connectToMQTT()) {
      mqttStatus = true;
      Serial.println("MQTT reconnected successfully!");
      break;
    } else {
      attempts++;
      Serial.printf("MQTT reconnection attempt %d failed\n", attempts);
      delay(5000);
    }
  }

  if (!mqttClient.connected()) {
    Serial.println("MQTT reconnection failed after 3 attempts!");
    mqttStatus = false;
  }
}

// Process MQTT commands
void processCommand(String command) {
  StaticJsonDocument<200> doc;
  DeserializationError error = deserializeJson(doc, command);

  if (error) {
    Serial.println("Failed to parse command JSON");
    sendResponse("Invalid JSON format", false);
    return;
  }

  String action = doc["action"];

  if (action == "set_interval") {
    if (doc.containsKey("value") && doc.containsKey("unit")) {
      unsigned long value = doc["value"];
      String unit = doc["unit"];

      unsigned long newInterval;
      if (unit == "seconds" || unit == "s") {
        newInterval = value * 1000;
      } else if (unit == "minutes" || unit == "m") {
        newInterval = value * 60 * 1000;
      } else {
        sendResponse("Invalid unit. Use 'seconds', 's', 'minutes', or 'm'", false);
        return;
      }

      setInterval(newInterval);
    } else {
      sendResponse("Missing 'value' or 'unit' parameter", false);
    }
  }
  else if (action == "get_interval") {
    String response = "{\"action\":\"get_interval\",\"interval_ms\":" + String(interval) +
                     ",\"interval_seconds\":" + String(interval/1000) +
                     ",\"interval_minutes\":" + String(interval/60000) + "}";
    mqttClient.publish(topic_response, response.c_str());
  }
  else if (action == "get_status") {
    String response = "{\"action\":\"get_status\",\"wifi\":" + String(wifiStatus ? "true" : "false") +
                     ",\"mqtt\":" + String(mqttStatus ? "true" : "false") +
                     ",\"dht11\":" + String(dhtStatus ? "true" : "false") +
                     ",\"bh1750\":" + String(bh1750Status ? "true" : "false") +
                     ",\"ina219\":" + String(ina219Status ? "true" : "false") +
                     ",\"uptime\":" + String(millis()/1000) +
                     ",\"free_heap\":" + String(ESP.getFreeHeap()) + "}";
    mqttClient.publish(topic_response, response.c_str());
  }
  else if (action == "force_reading") {
    Serial.println("Force reading requested via MQTT");
    previousMillis = 0; // Reset timer to trigger immediate reading
    sendResponse("Force reading triggered");
  }
  else {
    sendResponse("Unknown action: " + action, false);
  }
}

// Set new interval with validation
void setInterval(unsigned long newInterval) {
  if (newInterval < MIN_INTERVAL) {
    sendResponse("Interval too small. Minimum is " + String(MIN_INTERVAL/1000) + " seconds", false);
    return;
  }

  if (newInterval > MAX_INTERVAL) {
    sendResponse("Interval too large. Maximum is " + String(MAX_INTERVAL/60000) + " minutes", false);
    return;
  }

  unsigned long oldInterval = interval;
  interval = newInterval;

  Serial.printf("Interval changed from %lu ms to %lu ms\n", oldInterval, interval);
  Serial.printf("New interval: %.1f seconds (%.2f minutes)\n", interval/1000.0, interval/60000.0);

  String response = "{\"action\":\"set_interval\",\"old_interval\":" + String(oldInterval) +
                   ",\"new_interval\":" + String(interval) +
                   ",\"seconds\":" + String(interval/1000) +
                   ",\"minutes\":" + String(interval/60000) + "}";

  sendResponse(response);

  // Update status with new interval
  String statusMsg = "{\"device_id\":\"" + String(mqtt_client_id) + "\",\"status\":\"interval_updated\",\"interval\":" + String(interval) + "}";
  mqttClient.publish(topic_status, statusMsg.c_str());
}

// Send response message
void sendResponse(String message, bool success) {
  String response;
  if (message.startsWith("{")) {
    // Already JSON format
    response = message;
  } else {
    // Create JSON response
    response = "{\"success\":" + String(success ? "true" : "false") + ",\"message\":\"" + message + "\",\"timestamp\":" + String(millis()) + "}";
  }

  bool result = mqttClient.publish(topic_response, response.c_str());
  if (result) {
    Serial.println("Response sent: " + response);
  } else {
    Serial.println("Failed to send response");
  }
}