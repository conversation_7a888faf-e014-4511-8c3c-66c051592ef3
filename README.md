# Smart Lamp MQTT IoT System

## Deskripsi Proyek

Smart Lamp adalah sistem IoT berbasis ESP32 yang mengintegrasikan berbagai sensor untuk monitoring lingkungan dan konsumsi daya. Sistem ini dapat mengukur suhu, kele<PERSON><PERSON>, intensitas cahaya, tegangan, dan arus listrik, kemudian mengirim data tersebut melalui protokol MQTT ke HiveMQ broker untuk monitoring dan analisis real-time.

## Komponen Hardware

### Mikrokontroler
- **ESP32 Development Board**: Sebagai otak utama sistem yang mengontrol semua sensor dan koneksi WiFi

### Sensor yang Digunakan
1. **DHT11** - Sensor suhu dan kelembaban
   - Pin Data: GPIO4
   - Range suhu: 0-50°C
   - Range kelembaban: 20-90% RH
   - Akurasi: ±2°C, ±5% RH

2. **BH1750** - Sensor cahaya digital (I2C)
   - Range: 0-65535 lux
   - Interface: I2C
   - Address: 0x23 atau 0x5C

3. **INA219** - Sensor tegangan dan arus (I2C)
   - Range tegangan: 0-26V
   - Range arus: ±3.2A
   - Interface: I2C
   - Address: 0x40, 0x41, 0x44, atau 0x45

### Koneksi I2C
- **SDA**: GPIO21
- **SCL**: GPIO22

## Cara Kerja Sistem

### 1. Inisialisasi System
```
Power On → System Check → I2C Scan → Sensor Initialization → WiFi Connection
```

**Proses Startup:**
- Sistem melakukan pengecekan power supply
- Inisialisasi komunikasi I2C pada pin yang telah ditentukan
- Scanning I2C bus untuk mendeteksi perangkat yang terhubung
- Verifikasi dan inisialisasi setiap sensor
- Koneksi ke jaringan WiFi
- Menampilkan status sistem secara keseluruhan

### 2. Loop Utama (Setiap 10 Menit)

#### a. Verifikasi Koneksi
- **WiFi Verification**: Memeriksa status koneksi WiFi, jika terputus akan mencoba reconnect
- **Sensor Verification**: Memverifikasi setiap sensor masih berfungsi dengan baik

#### b. Pembacaan Sensor
1. **DHT11 Reading**:
   - Membaca suhu dalam Celsius
   - Membaca kelembaban dalam persen
   - Validasi: suhu (-40°C sampai 80°C), kelembaban (0% sampai 100%)

2. **BH1750 Reading**:
   - Membaca intensitas cahaya dalam lux
   - Validasi: 0 sampai 100,000 lux

3. **INA219 Reading**:
   - Membaca tegangan bus dalam Volt
   - Membaca arus dalam miliAmpere
   - Validasi: tegangan (0-32V), arus (-3200mA sampai +3200mA)

#### c. Validasi Data
Sistem melakukan validasi data untuk memastikan:
- Tidak ada nilai NaN (Not a Number)
- Semua nilai berada dalam range yang wajar
- Sensor berfungsi dengan normal

#### d. Pengiriman Data
Jika WiFi dan MQTT terhubung serta data valid:
- Data dikemas dalam format JSON
- Dikirim ke HiveMQ broker melalui MQTT protocol
- Menampilkan status pengiriman dan waktu response
- Mengirim heartbeat untuk monitoring koneksi

### 3. Monitoring dan Diagnostik

#### a. Status Tracking
Sistem melacak status real-time dari:
- ✓/✗ WiFi Connection
- ✓/✗ MQTT Connection
- ✓/✗ DHT11 Sensor
- ✓/✗ BH1750 Sensor
- ✓/✗ INA219 Sensor

#### b. Error Handling
- **Sensor Failure**: Sistem tetap berjalan meskipun ada sensor yang gagal
- **WiFi Disconnection**: Otomatis mencoba reconnect
- **MQTT Disconnection**: Otomatis mencoba reconnect ke broker
- **Invalid Readings**: Data tidak valid tidak akan dikirim
- **MQTT Errors**: Menampilkan status koneksi dan pesan kesalahan

#### c. System Information
- Free heap memory monitoring
- System uptime tracking
- Signal strength monitoring (RSSI)
- Network information (IP, Gateway, DNS)

## Konfigurasi

### WiFi Configuration
```cpp
const char *ssid = "YOUR_WIFI_SSID";
const char *password = "YOUR_WIFI_PASSWORD";
```

### MQTT Configuration (Flexible)
```cpp
// MQTT Mode Selection
const bool use_tls = true; // Set to false for non-TLS connection

// HiveMQ Cloud (TLS) - Secure, private broker
const char *mqtt_server_tls = "79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud";
const int mqtt_port_tls = 8883;
const char *mqtt_user_tls = "smart_lamp";
const char *mqtt_password_tls = "Lampupintar123";

// HiveMQ Public (Non-TLS) - Public broker, no authentication
const char *mqtt_server_plain = "broker.hivemq.com";
const int mqtt_port_plain = 1883;

// MQTT Topics
const char *topic_sensors = "smartlamp/sensors/data";
const char *topic_status = "smartlamp/status";
const char *topic_heartbeat = "smartlamp/heartbeat";
const char *topic_command = "smartlamp/command";
const char *topic_response = "smartlamp/response";
```

**Pilihan Konfigurasi:**
- **TLS Mode** (`use_tls = true`): Menggunakan HiveMQ Cloud dengan enkripsi SSL/TLS
- **Plain Mode** (`use_tls = false`): Menggunakan HiveMQ Public tanpa enkripsi

### Timing Configuration
```cpp
unsigned long interval = 600000; // Default: 10 menit (600,000 ms)
const unsigned long MIN_INTERVAL = 5000;    // Minimum 5 detik
const unsigned long MAX_INTERVAL = 3600000; // Maximum 1 jam
```

### Remote Control via MQTT
Sistem dapat dikontrol secara remote melalui MQTT commands:
- **Set Interval**: Mengubah interval pengambilan data
- **Get Status**: Mendapatkan status sistem
- **Force Reading**: Memaksa pembacaan sensor segera

## Instalasi dan Setup

### 1. Hardware Setup
1. Hubungkan DHT11:
   - VCC → 3.3V
   - GND → GND
   - Data → GPIO4
   - Tambahkan resistor pull-up 4.7kΩ antara Data dan VCC

2. Hubungkan BH1750:
   - VCC → 3.3V
   - GND → GND
   - SDA → GPIO21
   - SCL → GPIO22

3. Hubungkan INA219:
   - VIN → 3.3V
   - GND → GND
   - SDA → GPIO21
   - SCL → GPIO22

### 2. Software Setup

#### Library Dependencies
```ini
lib_deps =
    adafruit/DHT sensor library@^1.4.4
    claws/BH1750@^1.3.0
    adafruit/Adafruit INA219@^1.2.1
    adafruit/Adafruit BusIO@^1.14.1
    knolleary/PubSubClient@^2.8
    bblanchon/ArduinoJson@^6.21.3
```

#### Build dan Upload
```bash
# Install dependencies
pio lib install

# Build project
pio run

# Upload to ESP32
pio run --target upload

# Monitor serial output
pio device monitor
```

### 3. MQTT Setup

#### Mode TLS (Default - Recommended)
1. Menggunakan HiveMQ Cloud dengan enkripsi SSL/TLS
2. Memerlukan username dan password untuk autentikasi
3. Lebih aman untuk data sensitif
4. Set `use_tls = true` di kode

#### Mode Plain (Alternative)
1. Menggunakan HiveMQ Public broker tanpa enkripsi
2. Tidak memerlukan autentikasi
3. Cocok untuk testing dan development
4. Set `use_tls = false` di kode

#### MQTT Topics (Sama untuk kedua mode):
- Data sensor: `smartlamp/sensors/data`
- Status sistem: `smartlamp/status`
- Heartbeat: `smartlamp/heartbeat`
- Command input: `smartlamp/command`
- Response output: `smartlamp/response`

## Format Data yang Dikirim

Data dikirim dalam format JSON melalui MQTT:
```json
{
  "timestamp": 123456789,
  "temperature": 25.5,
  "humidity": 60.2,
  "light": 150.0,
  "voltage": 3.300,
  "current": 120.5,
  "device_id": "ESP32_SmartLamp_001"
}
```

**Field JSON:**
- `timestamp`: Waktu dalam milliseconds sejak boot
- `temperature`: Suhu dalam °C (1 desimal)
- `humidity`: Kelembaban dalam % (1 desimal)
- `light`: Intensitas cahaya dalam lux (1 desimal)
- `voltage`: Tegangan dalam V (3 desimal)
- `current`: Arus dalam mA (1 desimal)
- `device_id`: Identifier unik perangkat

## MQTT Remote Control

### Command Format
Commands dikirim ke topic `smartlamp/command` dalam format JSON:

#### 1. Set Interval (Mengubah Waktu Pengambilan Data)
```json
{
  "action": "set_interval",
  "value": 30,
  "unit": "seconds"
}
```
atau
```json
{
  "action": "set_interval",
  "value": 5,
  "unit": "minutes"
}
```

**Units yang didukung:**
- `"seconds"` atau `"s"` - dalam detik (5-3600 detik)
- `"minutes"` atau `"m"` - dalam menit (0.1-60 menit)

#### 2. Get Current Interval
```json
{
  "action": "get_interval"
}
```

#### 3. Get System Status
```json
{
  "action": "get_status"
}
```

#### 4. Force Sensor Reading
```json
{
  "action": "force_reading"
}
```

### Response Format
Response dikirim ke topic `smartlamp/response`:

#### Set Interval Response
```json
{
  "action": "set_interval",
  "old_interval": 600000,
  "new_interval": 30000,
  "seconds": 30,
  "minutes": 0.5
}
```

#### Get Interval Response
```json
{
  "action": "get_interval",
  "interval_ms": 30000,
  "interval_seconds": 30,
  "interval_minutes": 0.5
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Interval too small. Minimum is 5 seconds",
  "timestamp": 123456789
}
```

## Monitoring Sistem

### Serial Monitor Output
```
=== ESP32 Smart Lamp MQTT System Starting ===
Chip Model: ESP32-D0WDQ6
CPU Frequency: 240 MHz
Free Heap: 295516 bytes
MQTT Broker: broker.hivemq.com:1883

I2C device found at address 0x23  // BH1750
I2C device found at address 0x40  // INA219

=== System Status ===
WiFi: ✓ Connected
  IP: *************
  RSSI: -45 dBm
MQTT: ✓ Connected
  Broker: broker.hivemq.com:1883
  Client: ESP32_SmartLamp_001
DHT11: ✓ Working
BH1750: ✓ Working
INA219: ✓ Working

=== Sensor Reading Cycle ===
Temperature: 25.5°C
Humidity: 60.2%
Light Level: 150.0 lux
Bus Voltage: 3.300 V
Current: 120.5 mA
JSON Payload: {"timestamp":123456,"temperature":25.5,"humidity":60.2,"light":150.0,"voltage":3.300,"current":120.5,"device_id":"ESP32_SmartLamp_001"}
✓ Data sent successfully to MQTT!
Topic: smartlamp/sensors/data
```

## Troubleshooting

### Common Issues

1. **Sensor not detected**
   - Periksa koneksi kabel
   - Pastikan power supply stabil (3.3V)
   - Gunakan I2C scanner untuk deteksi alamat

2. **WiFi connection failed**
   - Periksa SSID dan password
   - Pastikan sinyal WiFi cukup kuat
   - Reset ESP32 dan coba lagi

3. **Data not sent via MQTT**
   - Periksa koneksi internet
   - Pastikan HiveMQ broker dapat diakses
   - Check MQTT connection status di serial monitor
   - Coba ganti ke broker MQTT lain jika diperlukan

4. **Erratic sensor readings**
   - Periksa power supply noise
   - Pastikan grounding yang baik
   - Tambahkan capacitor filter jika diperlukan

### Diagnostic Commands
- Monitor I2C bus: Sistem otomatis scan alamat 0x01-0x7F
- Check sensor status: Lihat status tracking di serial monitor
- Memory usage: Free heap ditampilkan setiap cycle
- Network info: IP, Gateway, DNS, Signal strength

## Pengembangan Lanjutan

### Possible Enhancements
1. **Local Data Storage**: Tambah SD card untuk backup data
2. **Web Interface**: Buat web server untuk konfigurasi
3. **OTA Updates**: Implementasi Over-The-Air firmware updates
4. **Alarm System**: Tambah notifikasi untuk threshold tertentu
5. **Power Management**: Implementasi deep sleep untuk hemat baterai
6. **Additional Sensors**: Motion sensor, CO2 sensor, dll

### Code Structure
```
src/
├── main.cpp                 // Main application code
├── sensor_manager.cpp       // Sensor management (future)
├── wifi_manager.cpp         // WiFi management (future)
└── data_logger.cpp          // Data logging (future)
```

## Lisensi
Proyek ini menggunakan lisensi MIT - lihat file LICENSE untuk detail.

## Kontributor
- Pak Yanuar (Project Supervisor)
- Tim Telkom University

---

## Arsitektur Data Flow

```
Sensors → ESP32 → WiFi → MQTT Protocol → HiveMQ Broker → MQTT Clients/Dashboard/Analysis
```

### MQTT Topics Structure:
- `smartlamp/sensors/data` - Data sensor utama dalam format JSON
- `smartlamp/status` - Status sistem dan informasi perangkat
- `smartlamp/heartbeat` - Heartbeat monitoring untuk tracking koneksi
- `smartlamp/command` - Menerima command untuk kontrol remote
- `smartlamp/response` - Response dari command yang dikirim

### Keunggulan MQTT vs HTTP:
1. **Lightweight Protocol** - Overhead lebih kecil dibanding HTTP
2. **Real-time Communication** - Push notification langsung ke subscribers
3. **Better for IoT** - Dirancang khusus untuk komunikasi M2M
4. **Persistent Connection** - Koneksi tetap terbuka, lebih efisien
5. **Quality of Service** - Garanteed delivery dengan QoS levels

## Testing dan Monitoring

### 1. HiveMQ Web Client
Gunakan HiveMQ Web Client untuk monitoring dan testing:
- **URL**: https://www.hivemq.com/demos/websocket-client/
- **Host**: `79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud` (TLS mode)
- **Port**: `8884` (WebSocket over TLS)
- **Username**: `smart_lamp`
- **Password**: `Lampupintar123`

### 2. MQTT Explorer (Desktop App)
Download MQTT Explorer untuk monitoring yang lebih lengkap:
- **Download**: http://mqtt-explorer.com/
- Konfigurasi sama dengan HiveMQ Web Client

### 3. Manual MQTT Command Examples

#### Untuk TLS Mode:
```bash
# Publish command (dengan mosquitto)
mosquitto_pub -h 79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud -p 8883 --cafile ca.crt -u smart_lamp -P Lampupintar123 -t smartlamp/command -m '{"action":"set_interval","value":15,"unit":"seconds"}'

# Subscribe response
mosquitto_sub -h 79102f6522304514a10e2b4a7caa2bb2.s1.eu.hivemq.cloud -p 8883 --cafile ca.crt -u smart_lamp -P Lampupintar123 -t smartlamp/response
```

#### Untuk Plain Mode:
```bash
# Publish command
mosquitto_pub -h broker.hivemq.com -t smartlamp/command -m '{"action":"set_interval","value":15,"unit":"seconds"}'

# Subscribe response
mosquitto_sub -h broker.hivemq.com -t smartlamp/response
```

### 4. Contoh Command JSON
```json
# Set interval 30 detik
{"action": "set_interval", "value": 30, "unit": "seconds"}

# Set interval 2 menit
{"action": "set_interval", "value": 2, "unit": "minutes"}

# Get status sistem
{"action": "get_status"}

# Force reading segera
{"action": "force_reading"}
```

## Konfigurasi Mode MQTT

### Mengubah Mode TLS/Plain
Edit file `src/main.cpp` pada baris:
```cpp
const bool use_tls = true; // Set to false for non-TLS connection
```

**TLS Mode (Recommended):**
- Set `use_tls = true`
- Menggunakan HiveMQ Cloud dengan enkripsi
- Memerlukan username/password
- Lebih aman untuk production

**Plain Mode (Testing):**
- Set `use_tls = false`
- Menggunakan HiveMQ Public tanpa enkripsi
- Tidak memerlukan autentikasi
- Cocok untuk development/testing

### Konfigurasi WiFi
Edit kredensial WiFi di `src/main.cpp`:
```cpp
const char *ssid = "YOUR_WIFI_SSID";
const char *password = "YOUR_WIFI_PASSWORD";
```

**Note**: Pastikan untuk mengganti SSID dan password WiFi dengan konfigurasi yang sesuai sebelum deployment.
